import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft, MapPin, Phone, CreditCard, Truck, Edit3,
  ShoppingBag, Clock, Shield, CheckCircle, Star, Sparkles,
  Package, User, CreditCard as CardIcon, Banknote
} from 'lucide-react';
import { useCartStore } from '../../stores/cartStore';
import { useOrdersStore } from '../../stores/ordersStore';

const OrderCheckoutPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const { clearCart, getItemsBySupplier, getTotalPrice } = useCartStore();
  const { addMultipleOrders } = useOrdersStore();

  // Get cart data from location state or use current cart
  const cartData = location.state || {};
  const locationItemsBySupplier = cartData.itemsBySupplier || {};
  const currentCartItemsBySupplier = getItemsBySupplier();

  // Use location state data if available, otherwise use current cart
  const itemsBySupplier = Object.keys(locationItemsBySupplier).length > 0
    ? locationItemsBySupplier
    : currentCartItemsBySupplier;

  const totalWithoutFee = cartData.totalWithoutFee || getTotalPrice();

  // If no cart data anywhere, redirect back to home
  const supplierIds = Object.keys(itemsBySupplier);

  // Temporarily disabled redirect for testing
  // useEffect(() => {
  //   if (supplierIds.length === 0) {
  //     console.log('No cart items found, redirecting to home');
  //     navigate('/customer/home');
  //   }
  // }, [supplierIds.length, navigate]);

  // Form states
  const [address, setAddress] = useState('Nablus, Palestine');
  const [selectedLocation, setSelectedLocation] = useState<{lat: number, lng: number, address: string} | null>(null);
  const [phone, setPhone] = useState('');
  const [notes, setNotes] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card'>('cash');
  const [cardNumber, setCardNumber] = useState('');
  const [cardCvv, setCardCvv] = useState('');
  const [promoCode, setPromoCode] = useState('');

  // Calculate totals
  const deliveryFee = 12;
  const promoDiscount = promoCode === 'WASEL10' ? 10 : 0;

  const subtotal = Object.values(itemsBySupplier).flat().reduce((sum: number, item: any) => {
    return sum + (item.finalPrice * item.qty);
  }, 0);

  const total = subtotal + deliveryFee - promoDiscount;

  const isFormValid = () => {
    return address.trim() &&
           phone.trim() &&
           (paymentMethod === 'cash' || (cardNumber.trim() && cardCvv.trim()));
  };

  const handleSelectLocation = () => {
    navigate('/customer/select-location?type=delivery');
  };

  // Listen for location selection from SelectLocationPage
  useEffect(() => {
    const handleLocationUpdate = (event: CustomEvent) => {
      const locationData = event.detail;

      if (locationData && locationData.lat && locationData.lng && locationData.address) {
        setSelectedLocation(locationData);
        setAddress(locationData.address);
        // Clear localStorage after successful event
        localStorage.removeItem('selectedDeliveryLocation');
      }
    };

    // Check localStorage for location data (backup method)
    const checkStoredLocation = () => {
      const storedLocation = localStorage.getItem('selectedDeliveryLocation');
      if (storedLocation) {
        try {
          const locationData = JSON.parse(storedLocation);
          if (locationData && locationData.lat && locationData.lng && locationData.address) {
            setSelectedLocation(locationData);
            setAddress(locationData.address);
            localStorage.removeItem('selectedDeliveryLocation');
          }
        } catch (error) {
          localStorage.removeItem('selectedDeliveryLocation');
        }
      }
    };

    window.addEventListener('locationSelected', handleLocationUpdate as EventListener);

    // Check for stored location immediately
    checkStoredLocation();

    // Also check after a short delay in case of timing issues
    const timeoutId = setTimeout(checkStoredLocation, 200);

    return () => {
      window.removeEventListener('locationSelected', handleLocationUpdate as EventListener);
      clearTimeout(timeoutId);
    };
  }, []);

  const handlePlaceOrder = () => {
    if (!isFormValid()) {
      alert('Please fill in all required fields');
      return;
    }

    // Create order data for each supplier
    const orders = supplierIds.map(supplierId => {
      const supplierItems = itemsBySupplier[supplierId];
      const supplier = {
        id: supplierId,
        name: supplierItems[0]?.supplierName || 'Supplier'
      };

      const orderSubtotal = supplierItems.reduce((sum: number, item: any) => sum + (item.finalPrice * item.qty), 0);
      const orderDeliveryFee = deliveryFee / supplierIds.length; // Split delivery fee
      const orderPromoDiscount = promoDiscount / supplierIds.length; // Split discount
      const orderTotal = orderSubtotal + orderDeliveryFee - orderPromoDiscount;

      return {
        id: `W-${Date.now().toString().slice(-6)}-${supplierId}`,
        createdAt: new Date().toISOString(),
        items: supplierItems.map((item: any) => ({
          product: item.product,
          qty: item.qty,
          finalPrice: item.finalPrice,
          selectedAdditions: item.selectedAdditions,
          selectedSides: item.selectedSides,
          without: item.without,
          selectedSize: item.selectedSize,
          selectedColor: item.selectedColor
        })),
        supplier,
        subtotal: orderSubtotal,
        deliveryFee: orderDeliveryFee,
        promoDiscount: orderPromoDiscount,
        total: orderTotal,
        status: 'Pending' as const,
        address,
        phone,
        notes,
        paymentMethod,
        promoCode,
        estimatedTime: '45-60 mins',
        placedAt: new Date().toISOString(),
        // Include selected location data
        deliveryLocation: selectedLocation ? {
          lat: selectedLocation.lat,
          lng: selectedLocation.lng,
          address: selectedLocation.address
        } : undefined,
        driverLocation: {
          lat: 32.2211,
          lng: 35.2544,
          address: 'Near Al-Najah University'
        }// Default driver location
      };
    });

    // Add orders to the orders store
    addMultipleOrders(orders);

    // Clear cart after successful order
    clearCart();

    // Navigate to confirmation page with all orders
    navigate('/customer/order-confirmation', { state: { allOrders: orders } });
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-500/30 to-red-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-pink-500/30 to-purple-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
          className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-blue-500/30 to-cyan-600/30 rounded-full blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, -100, -20],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Content Overlay */}
      <div className="relative z-10">
        {/* Enhanced Header */}
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="bg-white/10 backdrop-blur-xl border-b border-white/20 shadow-2xl"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => navigate(-1)}
                  className="p-3 hover:bg-white/20 rounded-xl transition-all duration-300 backdrop-blur-sm border border-white/10"
                >
                  <ArrowLeft className="w-5 h-5 text-white" />
                </motion.button>
                <div>
                  <h1 className="text-3xl font-bold text-white flex items-center gap-3">
                    <ShoppingBag className="w-8 h-8 text-purple-300" />
                    Checkout
                  </h1>
                  <p className="text-purple-200 mt-1">Complete your order securely</p>
                </div>
              </div>

              {/* Security Badge */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3, type: "spring" }}
                className="flex items-center gap-2 bg-green-500/20 text-green-300 px-4 py-2 rounded-full border border-green-400/30"
              >
                <Shield className="w-4 h-4" />
                <span className="text-sm font-medium">Secure Checkout</span>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Main Content Container */}
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Order Summary */}
              <motion.div
                initial={{ x: -50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-500/20 rounded-xl">
                      <Package className="w-6 h-6 text-purple-300" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">Order Summary</h2>
                      <p className="text-purple-200 text-sm">Review your items</p>
                    </div>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => navigate(-1)}
                    className="text-purple-300 hover:text-white flex items-center gap-2 text-sm bg-white/10 px-4 py-2 rounded-xl border border-white/20 transition-all duration-300"
                  >
                    <Edit3 className="w-4 h-4" />
                    Edit Cart
                  </motion.button>
                </div>

                <div className="space-y-6">
                  {supplierIds.map((supplierId, index) => {
                    const supplierItems = itemsBySupplier[supplierId];
                    const supplierName = supplierItems[0]?.supplierName || 'Supplier';

                    return (
                      <motion.div
                        key={supplierId}
                        initial={{ y: 20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ duration: 0.4, delay: 0.2 + index * 0.1 }}
                        className="space-y-4"
                      >
                        <div className="flex items-center gap-3 pb-3 border-b border-white/20">
                          <div className="p-2 bg-orange-500/20 rounded-lg">
                            <Star className="w-4 h-4 text-orange-300" />
                          </div>
                          <span className="font-semibold text-white text-lg">From: {supplierName}</span>
                        </div>

                        <div className="space-y-3">
                          {supplierItems.map((item: any, itemIndex: number) => {
                            const formatCustomizations = () => {
                              const customizations = [];

                              // Restaurant options
                              if (item.selectedAdditions?.length > 0) {
                                customizations.push(`+${item.selectedAdditions.map((a: any) => a.name).join(', ')}`);
                              }
                              if (item.selectedSides?.length > 0) {
                                customizations.push(`Sides: ${item.selectedSides.map((s: any) => s.name).join(', ')}`);
                              }
                              if (item.without?.length > 0) {
                                customizations.push(`Without: ${item.without.join(', ')}`);
                              }

                              // Clothing options
                              if (item.selectedSize) {
                                customizations.push(`Size: ${item.selectedSize}`);
                              }
                              if (item.selectedColor) {
                                customizations.push(`Color: ${item.selectedColor}`);
                              }

                              // Custom options (if any)
                              if (item.customSelections) {
                                Object.entries(item.customSelections).forEach(([key, value]: [string, any]) => {
                                  if (value && value !== '') {
                                    if (Array.isArray(value)) {
                                      customizations.push(`${key}: ${value.join(', ')}`);
                                    } else {
                                      customizations.push(`${key}: ${value}`);
                                    }
                                  }
                                });
                              }

                              return customizations.length > 0 ? ` (${customizations.join(' • ')})` : '';
                            };

                            return (
                              <motion.div
                                key={item.id}
                                initial={{ x: -20, opacity: 0 }}
                                animate={{ x: 0, opacity: 1 }}
                                transition={{ duration: 0.3, delay: 0.3 + itemIndex * 0.05 }}
                                className="flex justify-between items-start py-3 px-4 bg-white/5 rounded-xl border border-white/10"
                              >
                                <div className="flex-1">
                                  <div className="flex items-center gap-2">
                                    <span className="bg-purple-500/20 text-purple-300 px-2 py-1 rounded-lg text-sm font-medium">
                                      {item.qty}×
                                    </span>
                                    <span className="font-semibold text-white">{item.product.name}</span>
                                  </div>
                                  {formatCustomizations() && (
                                    <div className="text-sm text-purple-200 mt-1 ml-8">{formatCustomizations()}</div>
                                  )}
                                </div>
                                <div className="text-right">
                                  <span className="font-bold text-white text-lg">₪{(item.finalPrice * item.qty).toFixed(2)}</span>
                                </div>
                              </motion.div>
                            );
                          })}
                        </div>

                        {supplierIds.length > 1 && index < supplierIds.length - 1 && (
                          <div className="border-b border-white/10 pb-4"></div>
                        )}
                      </motion.div>
                    );
                  })}
                </div>
              </motion.div>

              {/* Delivery Address */}
              <motion.div
                initial={{ x: -50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-blue-500/20 rounded-xl">
                    <MapPin className="w-6 h-6 text-blue-300" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-white">Delivery Address</h2>
                    <p className="text-blue-200 text-sm">Where should we deliver your order?</p>
                  </div>
                </div>

                <div className="space-y-4">
                  {/* Map Location Selector */}
                  <div>
                    <label className="block text-sm font-medium text-white mb-3 flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      Select Location on Map
                    </label>
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleSelectLocation}
                      className="w-full p-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-white/20 rounded-xl text-white hover:from-blue-500/30 hover:to-purple-500/30 transition-all duration-300 flex items-center justify-center gap-3"
                    >
                      <MapPin className="w-5 h-5 text-blue-300" />
                      <span className="font-medium">
                        {selectedLocation ? 'Change Location on Map' : 'Set Location on Map'}
                      </span>
                    </motion.button>

                    {selectedLocation && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-4 p-6 bg-gradient-to-br from-green-500/20 to-emerald-500/20 border border-green-400/30 rounded-2xl shadow-lg"
                      >
                        <div className="flex items-start gap-4">
                          <div className="p-3 bg-green-500/30 rounded-xl border border-green-400/30">
                            <MapPin className="w-6 h-6 text-green-200" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 text-green-300 mb-3">
                              <CheckCircle className="w-5 h-5" />
                              <span className="font-bold text-lg">Location Confirmed</span>
                            </div>

                            <div className="space-y-3">
                              <div className="p-3 bg-white/10 rounded-xl border border-white/20">
                                <p className="text-white font-semibold text-lg mb-1">📍 Delivery Address</p>
                                <p className="text-green-100 leading-relaxed">{selectedLocation.address}</p>
                              </div>

                              <div className="grid grid-cols-2 gap-3">
                                <div className="p-3 bg-white/5 rounded-xl border border-white/10">
                                  <p className="text-green-300 text-xs font-medium mb-1">LATITUDE</p>
                                  <p className="text-white font-mono text-sm">{selectedLocation.lat.toFixed(6)}</p>
                                </div>
                                <div className="p-3 bg-white/5 rounded-xl border border-white/10">
                                  <p className="text-green-300 text-xs font-medium mb-1">LONGITUDE</p>
                                  <p className="text-white font-mono text-sm">{selectedLocation.lng.toFixed(6)}</p>
                                </div>
                              </div>

                              <div className="flex items-center gap-2 text-green-200 text-sm">
                                <Sparkles className="w-4 h-4" />
                                <span>Precise location selected for accurate delivery</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </div>

                  {/* Address Text Field */}
                  <div>
                    <label className="block text-sm font-medium text-white mb-3 flex items-center gap-2">
                      <Edit3 className="w-4 h-4" />
                      Address Details
                    </label>
                    <textarea
                      value={address}
                      onChange={(e) => setAddress(e.target.value)}
                      placeholder="Enter your full address or additional details..."
                      className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent backdrop-blur-sm transition-all duration-300"
                      rows={3}
                      required
                    />
                    <p className="text-white/60 text-xs mt-2">
                      You can type your address manually or use the map selector above
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Contact Information */}
              <motion.div
                initial={{ x: -50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-green-500/20 rounded-xl">
                    <User className="w-6 h-6 text-green-300" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-white">Contact Information</h2>
                    <p className="text-green-200 text-sm">How can we reach you?</p>
                  </div>
                </div>
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-white mb-3 flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      placeholder="+970 XXX XXX XXX"
                      className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent backdrop-blur-sm transition-all duration-300"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-white mb-3 flex items-center gap-2">
                      <Sparkles className="w-4 h-4" />
                      Special Instructions (Optional)
                    </label>
                    <textarea
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Any special requests or notes for the driver..."
                      className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent backdrop-blur-sm transition-all duration-300"
                      rows={3}
                    />
                  </div>
                </div>
              </motion.div>

              {/* Payment Method */}
              <motion.div
                initial={{ x: -50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-yellow-500/20 rounded-xl">
                    <CreditCard className="w-6 h-6 text-yellow-300" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-white">Payment Method</h2>
                    <p className="text-yellow-200 text-sm">Choose your preferred payment option</p>
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <motion.label
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`flex items-center gap-3 p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                        paymentMethod === 'cash'
                          ? 'border-green-400 bg-green-500/20'
                          : 'border-white/20 bg-white/5 hover:bg-white/10'
                      }`}
                    >
                      <input
                        type="radio"
                        name="payment"
                        value="cash"
                        checked={paymentMethod === 'cash'}
                        onChange={(e) => setPaymentMethod(e.target.value as 'cash')}
                        className="w-5 h-5 text-green-500"
                      />
                      <Banknote className="w-5 h-5 text-green-300" />
                      <span className="text-white font-medium">Cash on Delivery</span>
                    </motion.label>

                    <motion.label
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`flex items-center gap-3 p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                        paymentMethod === 'card'
                          ? 'border-blue-400 bg-blue-500/20'
                          : 'border-white/20 bg-white/5 hover:bg-white/10'
                      }`}
                    >
                      <input
                        type="radio"
                        name="payment"
                        value="card"
                        checked={paymentMethod === 'card'}
                        onChange={(e) => setPaymentMethod(e.target.value as 'card')}
                        className="w-5 h-5 text-blue-500"
                      />
                      <CardIcon className="w-5 h-5 text-blue-300" />
                      <span className="text-white font-medium">Credit Card</span>
                    </motion.label>
                  </div>

                  <AnimatePresence>
                    {paymentMethod === 'card' && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4">
                          <div>
                            <label className="block text-sm font-medium text-white mb-3">
                              Card Number *
                            </label>
                            <input
                              type="text"
                              value={cardNumber}
                              onChange={(e) => setCardNumber(e.target.value)}
                              placeholder="1234 5678 9012 3456"
                              className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent backdrop-blur-sm transition-all duration-300"
                              required
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-white mb-3">
                              CVV *
                            </label>
                            <input
                              type="text"
                              value={cardCvv}
                              onChange={(e) => setCardCvv(e.target.value)}
                              placeholder="123"
                              className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent backdrop-blur-sm transition-all duration-300"
                              required
                            />
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </motion.div>
            </div>

            {/* Order Total Sidebar */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ x: 50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl sticky top-8"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-purple-500/20 rounded-xl">
                    <ShoppingBag className="w-6 h-6 text-purple-300" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">Order Total</h3>
                    <p className="text-purple-200 text-sm">Review your charges</p>
                  </div>
                </div>

                <div className="space-y-4 mb-6">
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    className="flex justify-between items-center py-2"
                  >
                    <span className="text-white/80">Subtotal</span>
                    <span className="text-white font-semibold">₪{subtotal.toFixed(2)}</span>
                  </motion.div>

                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    className="flex justify-between items-center py-2"
                  >
                    <span className="flex items-center gap-2 text-white/80">
                      <Truck className="w-4 h-4 text-blue-300" />
                      Delivery Fee
                    </span>
                    <span className="text-white font-semibold">₪{deliveryFee.toFixed(2)}</span>
                  </motion.div>

                  {/* Location Status */}
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.45 }}
                    className="flex justify-between items-center py-2"
                  >
                    <span className="flex items-center gap-2 text-white/80">
                      <MapPin className="w-4 h-4 text-orange-300" />
                      Delivery Location
                    </span>
                    <span className={`text-sm font-medium ${selectedLocation ? 'text-green-400' : 'text-orange-300'}`}>
                      {selectedLocation ? '✓ Set' : 'Required'}
                    </span>
                  </motion.div>

                  {promoDiscount > 0 && (
                    <motion.div
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.5 }}
                      className="flex justify-between items-center py-2 text-green-400"
                    >
                      <span className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4" />
                        Promo Discount
                      </span>
                      <span className="font-semibold">-₪{promoDiscount.toFixed(2)}</span>
                    </motion.div>
                  )}

                  <div className="border-t border-white/20 pt-4">
                    <motion.div
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.6 }}
                      className="flex justify-between items-center"
                    >
                      <span className="text-xl font-bold text-white">Total</span>
                      <span className="text-2xl font-bold text-purple-300">₪{total.toFixed(2)}</span>
                    </motion.div>
                  </div>
                </div>

                {/* Promo Code */}
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.7 }}
                  className="mb-6"
                >
                  <label className="block text-sm font-medium text-white mb-3 flex items-center gap-2">
                    <Sparkles className="w-4 h-4 text-yellow-300" />
                    Promo Code
                  </label>
                  <input
                    type="text"
                    value={promoCode}
                    onChange={(e) => setPromoCode(e.target.value)}
                    placeholder="Enter promo code"
                    className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent backdrop-blur-sm transition-all duration-300"
                  />
                  {promoCode === 'WASEL10' && (
                    <motion.p
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="text-green-400 text-sm mt-2 flex items-center gap-1"
                    >
                      <CheckCircle className="w-4 h-4" />
                      Promo code applied!
                    </motion.p>
                  )}
                </motion.div>

                <motion.button
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.8 }}
                  whileHover={{ scale: isFormValid() ? 1.02 : 1 }}
                  whileTap={{ scale: isFormValid() ? 0.98 : 1 }}
                  onClick={handlePlaceOrder}
                  disabled={!isFormValid()}
                  className={`w-full py-4 rounded-xl font-bold text-lg transition-all duration-300 ${
                    isFormValid()
                      ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 shadow-lg shadow-purple-500/25'
                      : 'bg-gray-600/50 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  <span className="flex items-center justify-center gap-2">
                    <ShoppingBag className="w-5 h-5" />
                    Place Order - ₪{total.toFixed(2)}
                  </span>
                </motion.button>

                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.9 }}
                  className="mt-4 text-center"
                >
                  <p className="text-white/60 text-sm flex items-center justify-center gap-2">
                    <Clock className="w-4 h-4" />
                    Estimated delivery: 45-60 minutes
                  </p>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderCheckoutPage;
